#!/usr/bin/env python3
"""
FramePack Standalone Preview Window
A standalone window containing image preview, latent animation preview, and output video preview
with control buttons for stopping and skipping generation.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
import time
import threading
import gc
from pathlib import Path

# Try to import video player libraries
try:
    from tkVideoPlayer import TkinterVideo
    TKVIDEO_AVAILABLE = True
    print("TkinterVideo loaded successfully. Video preview is enabled.")
except ImportError:
    try:
        from tkintervideo import TkinterVideo
        TKVIDEO_AVAILABLE = True
        print("tkintervideo loaded successfully. Video preview is enabled.")
    except ImportError:
        TKVIDEO_AVAILABLE = False
        TkinterVideo = None
        print("TkinterVideo not available. Video previews will be disabled.")

# Try to import PIL for image display
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available. Image previews will be disabled.")

# Try to import the custom GIF player
try:
    from tk_gif_player import TkGifPlayer
    GIF_PLAYER_AVAILABLE = True
except ImportError:
    GIF_PLAYER_AVAILABLE = False
    print("TkGifPlayer not available. Using TkinterVideo for latent previews.")


class FramePackPreviewWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Preview Window")
        self.root.geometry("600x800")
        self.root.minsize(400, 600)
        
        # Initialize variables
        self.init_variables()
        
        # Create UI elements
        self.create_ui_elements()
        
        # Start monitoring for updates
        self.start_monitoring()
        
        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Bind F5 key for window reset
        self.root.bind('<F5>', self.reset_window_size)
    
    def init_variables(self):
        """Initialize all variables"""
        # Video preview variables
        self.show_latent_preview = tk.BooleanVar(value=True)
        self.show_output_preview = tk.BooleanVar(value=True)
        self.show_image_preview = tk.BooleanVar(value=True)
        
        # Threading locks
        self.video_load_lock = threading.Lock()
        self.output_video_load_lock = threading.Lock()
        self.image_load_lock = threading.Lock()
        
        # Loading flags
        self.preview_loading = False
        self.output_preview_loading = False
        self.image_preview_loading = False
        
        # Current preview tracking
        self.current_preview = None
        self.current_output_preview = None
        self.current_image_preview = None
        
        # Video players
        self.latent_preview_player = None
        self.output_preview_player = None
        self.using_gif_player = False
        
        # Image display
        self.image_preview_label = None
        self.current_image_photo = None
        
        # Load counters
        self.latent_video_load_count = 0
        self.output_video_load_count = 0
    
    def create_ui_elements(self):
        """Create the UI elements"""
        # Create main frame with padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create control buttons frame at the top
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Control buttons
        self.force_stop_button = ttk.Button(
            control_frame,
            text="Force Stop All",
            command=self.force_stop_all,
            width=15
        )
        self.force_stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(
            control_frame,
            text="Stop",
            command=self.stop_generation,
            width=15
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.skip_button = ttk.Button(
            control_frame,
            text="Skip",
            command=self.skip_generation,
            width=15
        )
        self.skip_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # Create preview sections
        self.create_image_preview_section(main_frame)
        self.create_latent_preview_section(main_frame)
        self.create_output_preview_section(main_frame)
    
    def create_image_preview_section(self, parent):
        """Create the image preview section"""
        # Image Preview Frame
        image_preview_frame = ttk.LabelFrame(parent, text="Current Image")
        image_preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # Create frame for image display
        self.image_frame = ttk.Frame(image_preview_frame)
        self.image_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)
        
        if PIL_AVAILABLE:
            # Create label for image display
            self.image_preview_label = ttk.Label(
                self.image_frame,
                text="No image selected",
                anchor=tk.CENTER
            )
            self.image_preview_label.pack(fill=tk.BOTH, expand=True)
        else:
            ttk.Label(
                self.image_frame,
                text="Image preview not available - PIL not installed"
            ).pack(pady=10)
    
    def create_latent_preview_section(self, parent):
        """Create the latent preview section"""
        # Latent Preview Frame
        latent_preview_frame = ttk.LabelFrame(parent, text="Latent Preview")
        latent_preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # Create frame for video player
        self.video_frame = ttk.Frame(latent_preview_frame)
        self.video_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)
        
        # Initialize video player
        self.create_latent_video_player()
    
    def create_output_preview_section(self, parent):
        """Create the output video preview section"""
        # Output Video Preview Frame
        output_preview_frame = ttk.LabelFrame(parent, text="Output Video Preview")
        output_preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # Create frame for output video player
        self.output_video_frame = ttk.Frame(output_preview_frame)
        self.output_video_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)
        
        # Initialize output video player
        self.create_output_video_player()
    
    def create_latent_video_player(self):
        """Create the latent video player"""
        try:
            # First try the custom GIF player for better compatibility
            if GIF_PLAYER_AVAILABLE:
                try:
                    self.latent_preview_player = TkGifPlayer(
                        master=self.video_frame, 
                        width=384, 
                        height=288
                    )
                    self.latent_preview_player.pack(fill=tk.BOTH, expand=True)
                    self.using_gif_player = True
                    print("Using GIF player for latent preview")
                    return
                except Exception as e:
                    print(f"Error creating GIF player: {e}")
            
            # Fall back to TkinterVideo
            if TKVIDEO_AVAILABLE:
                self.latent_preview_player = TkinterVideo(
                    master=self.video_frame, 
                    scaled=True, 
                    keep_aspect=True
                )
                self.latent_preview_player.pack(fill=tk.BOTH, expand=True)
                self.latent_preview_player.config(height=288)
                self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)
                self.using_gif_player = False
                print("Using TkinterVideo for latent preview")
            else:
                ttk.Label(
                    self.video_frame,
                    text="Video preview not available - TkinterVideo not installed"
                ).pack(pady=10)
        except Exception as e:
            print(f"Error initializing latent video player: {e}")
            ttk.Label(
                self.video_frame,
                text="Video player initialization failed"
            ).pack(pady=10)
    
    def create_output_video_player(self):
        """Create the output video player"""
        try:
            if TKVIDEO_AVAILABLE:
                self.output_preview_player = TkinterVideo(
                    master=self.output_video_frame,
                    scaled=True,
                    keep_aspect=True
                )
                self.output_preview_player.pack(fill=tk.BOTH, expand=True)
                self.output_preview_player.config(height=480)
                self.output_preview_player.bind("<Button-1>", self.toggle_output_preview_playback)
                print("Output video player created")
            else:
                ttk.Label(
                    self.output_video_frame,
                    text="Video preview not available - TkinterVideo not installed"
                ).pack(pady=10)
        except Exception as e:
            print(f"Error initializing output video player: {e}")
            ttk.Label(
                self.output_video_frame,
                text="Video player initialization failed"
            ).pack(pady=10)

    def start_monitoring(self):
        """Start monitoring for preview updates"""
        if PIL_AVAILABLE:
            self.root.after(1000, self.update_image_preview)

        if self.latent_preview_player is not None:
            self.root.after(1000, self.update_latent_preview)

        if self.output_preview_player is not None:
            self.root.after(2000, self.update_output_preview)

    def update_image_preview(self):
        """Update the image preview with the current input image"""
        try:
            if not self.show_image_preview.get():
                self.root.after(1000, self.update_image_preview)
                return

            # Find the current input image using multiple strategies
            current_input_image = self.find_current_input_image()
            
            if current_input_image and current_input_image != self.current_image_preview:
                self.load_image_preview(current_input_image)
                self.current_image_preview = current_input_image

        except Exception as e:
            print(f"Error updating image preview: {e}")

        # Schedule next update
        self.root.after(1000, self.update_image_preview)

    def find_current_input_image(self):
        """Find the current input image being processed using multiple detection strategies"""
        try:
            import glob
            import re

            # Strategy 1: Look for job-specific start images (most reliable)
            # Pattern: YYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_start.png
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                start_pattern = os.path.join(outputs_dir, "*_start.png")
                start_files = glob.glob(start_pattern)

                # Filter for job-specific pattern and get most recent
                job_specific_files = []
                for file_path in start_files:
                    filename = os.path.basename(file_path)
                    if re.match(r'^\d+_\d+_\d+_\d+_\d+_\d+_start\.png$', filename):
                        job_specific_files.append((file_path, os.path.getmtime(file_path)))

                if job_specific_files:
                    # Get the most recent job-specific file
                    latest_file = max(job_specific_files, key=lambda x: x[1])
                    print(f"Found current input image: {os.path.basename(latest_file[0])} (job-specific)")
                    return latest_file[0]

            # Strategy 2: Look for job-specific start images in current directory
            start_files = glob.glob("*_start.png")
            job_specific_files = []
            for file_path in start_files:
                filename = os.path.basename(file_path)
                if re.match(r'^\d+_\d+_\d+_\d+_\d+_\d+_start\.png$', filename):
                    job_specific_files.append((file_path, os.path.getmtime(file_path)))

            if job_specific_files:
                latest_file = max(job_specific_files, key=lambda x: x[1])
                print(f"Found current input image: {os.path.basename(latest_file[0])} (current dir)")
                return latest_file[0]

            # Strategy 3: Check temp folder for current processing images
            temp_dir = "temp"
            if os.path.exists(temp_dir):
                temp_files = []
                for file in os.listdir(temp_dir):
                    if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        if any(keyword in file.lower() for keyword in ['start', 'input', 'current', 'processing']):
                            file_path = os.path.join(temp_dir, file)
                            temp_files.append((file_path, os.path.getmtime(file_path)))

                if temp_files:
                    latest_file = max(temp_files, key=lambda x: x[1])
                    print(f"Found current input image: {os.path.basename(latest_file[0])} (temp)")
                    return latest_file[0]

            # Strategy 4: Look for recently modified images in input directory
            input_dir = "input"
            if os.path.exists(input_dir):
                input_files = []
                for ext in ['.png', '.jpg', '.jpeg', '.bmp', '.webp']:
                    input_files.extend(Path(input_dir).glob(f"*{ext}"))
                
                if input_files:
                    # Get the most recently accessed or modified file
                    latest_input = max(input_files, key=lambda p: max(p.stat().st_mtime, p.stat().st_atime))
                    print(f"Found current input image: {os.path.basename(latest_input)} (input dir)")
                    return str(latest_input)

            # Strategy 5: Generic start/input images (last resort)
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.webp']
            generic_files = []
            for file in os.listdir('.'):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    if any(keyword in file.lower() for keyword in ['start', 'input', 'source', 'original']):
                        # Skip if it's a job-specific file (already checked above)
                        if not re.match(r'^\d+_\d+_\d+_\d+_\d+_\d+_start\.png$', file):
                            generic_files.append((file, os.path.getmtime(file)))

            if generic_files:
                latest_file = max(generic_files, key=lambda x: x[1])
                print(f"Found current input image: {os.path.basename(latest_file[0])} (generic)")
                return latest_file[0]

            # Strategy 6: Fallback to most recent image in current directory
            current_dir = os.getcwd()
            image_paths = []
            for pattern in ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.webp']:
                image_paths.extend(Path(current_dir).glob(pattern))

            if image_paths:
                # Filter out output files and get most recent
                filtered_paths = [p for p in image_paths if not any(skip in str(p).lower() for skip in ['output', 'result', 'generated'])]
                if filtered_paths:
                    latest_image = max(filtered_paths, key=lambda p: p.stat().st_mtime)
                    return str(latest_image)

        except Exception as e:
            print(f"Error finding current input image: {e}")
        
        return None

    def load_image_preview(self, image_path):
        """Load and display an image preview"""
        try:
            if not PIL_AVAILABLE or not self.image_preview_label:
                return

            # Acquire lock to prevent concurrent loading
            if not self.image_load_lock.acquire(blocking=False):
                return

            try:
                # Load and resize image
                image = Image.open(image_path)

                # Calculate display size (fit to available space)
                display_width = 350
                display_height = 250

                # Calculate aspect ratio
                img_width, img_height = image.size
                aspect_ratio = img_width / img_height

                if aspect_ratio > display_width / display_height:
                    # Image is wider
                    new_width = display_width
                    new_height = int(display_width / aspect_ratio)
                else:
                    # Image is taller
                    new_height = display_height
                    new_width = int(display_height * aspect_ratio)

                # Resize image
                image = image.resize((new_width, new_height), Image.LANCZOS)

                # Convert to PhotoImage
                self.current_image_photo = ImageTk.PhotoImage(image)

                # Update label
                self.image_preview_label.config(
                    image=self.current_image_photo,
                    text=""
                )

                print(f"Loaded image preview: {os.path.basename(image_path)}")

            finally:
                self.image_load_lock.release()

        except Exception as e:
            print(f"Error loading image preview: {e}")
            if self.image_preview_label:
                self.image_preview_label.config(
                    image="",
                    text=f"Error loading image: {os.path.basename(image_path)}"
                )

    def update_latent_preview(self):
        """Update the latent preview with the latest video"""
        try:
            if not self.show_latent_preview.get() or not self.latent_preview_player:
                self.root.after(1000, self.update_latent_preview)
                return

            # Look for latent preview videos
            current_dir = os.getcwd()
            latent_dir = os.path.join(current_dir, 'latent_previews')

            if os.path.exists(latent_dir):
                # Find the most recent video file
                video_files = []
                for ext in ['*.mp4', '*.avi', '*.mov', '*.gif']:
                    video_files.extend(Path(latent_dir).glob(ext))

                if video_files:
                    latest_video = max(video_files, key=lambda p: p.stat().st_mtime)
                    latest_video_str = str(latest_video)

                    # Only update if it's a different video
                    if latest_video_str != self.current_preview:
                        self.load_latent_preview(latest_video_str)
                        self.current_preview = latest_video_str

        except Exception as e:
            print(f"Error updating latent preview: {e}")

        # Schedule next update
        self.root.after(1000, self.update_latent_preview)

    def load_latent_preview(self, video_path):
        """Load and play a latent preview video"""
        try:
            if not self.latent_preview_player:
                return

            # Acquire lock to prevent concurrent loading
            if not self.video_load_lock.acquire(blocking=False):
                return

            try:
                if self.using_gif_player:
                    # Using GIF player - only works with GIF files
                    if video_path.lower().endswith('.gif'):
                        self.latent_preview_player.load(video_path)
                        print(f"Loaded latent preview (GIF): {os.path.basename(video_path)}")
                    else:
                        # For non-GIF files, try to switch to TkinterVideo if available
                        if TKVIDEO_AVAILABLE:
                            print(f"Switching to TkinterVideo for MP4: {os.path.basename(video_path)}")
                            # Recreate player as TkinterVideo
                            self.latent_preview_player.destroy()
                            self.latent_preview_player = TkinterVideo(
                                master=self.video_frame,
                                scaled=True,
                                keep_aspect=True
                            )
                            self.latent_preview_player.pack(fill=tk.BOTH, expand=True)
                            self.latent_preview_player.config(height=288)
                            self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)
                            self.using_gif_player = False

                            # Load the video
                            self.latent_preview_player.load(video_path)
                            self.latent_preview_player.play()
                            print(f"Loaded latent preview (MP4): {os.path.basename(video_path)}")
                        else:
                            print(f"Cannot load MP4 file without TkinterVideo: {os.path.basename(video_path)}")
                else:
                    # Using TkinterVideo
                    self.latent_preview_player.load(video_path)
                    self.latent_preview_player.play()
                    print(f"Loaded latent preview: {os.path.basename(video_path)}")

            finally:
                self.video_load_lock.release()

        except Exception as e:
            print(f"Error loading latent preview: {e}")
            # Try to recover by recreating the player
            try:
                if TKVIDEO_AVAILABLE and not self.using_gif_player:
                    self.latent_preview_player.destroy()
                    self.latent_preview_player = TkinterVideo(
                        master=self.video_frame,
                        scaled=True,
                        keep_aspect=True
                    )
                    self.latent_preview_player.pack(fill=tk.BOTH, expand=True)
                    self.latent_preview_player.config(height=288)
                    self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)
            except:
                pass

    def update_output_preview(self):
        """Update the output video preview with the latest video"""
        try:
            if not self.show_output_preview.get() or not self.output_preview_player:
                self.root.after(2000, self.update_output_preview)
                return

            # Look for output videos
            current_dir = os.getcwd()
            outputs_dir = os.path.join(current_dir, 'outputs')

            if os.path.exists(outputs_dir):
                # Find the most recent video file
                video_files = []
                for ext in ['*.mp4', '*.avi', '*.mov']:
                    video_files.extend(Path(outputs_dir).glob(ext))

                if video_files:
                    latest_video = max(video_files, key=lambda p: p.stat().st_mtime)
                    latest_video_str = str(latest_video)

                    # Only update if it's a different video
                    if latest_video_str != self.current_output_preview:
                        self.load_output_preview(latest_video_str)
                        self.current_output_preview = latest_video_str

        except Exception as e:
            print(f"Error updating output preview: {e}")

        # Schedule next update
        self.root.after(2000, self.update_output_preview)

    def load_output_preview(self, video_path):
        """Load and play an output preview video"""
        try:
            if not self.output_preview_player:
                return

            # Acquire lock to prevent concurrent loading
            if not self.output_video_load_lock.acquire(blocking=False):
                return

            try:
                self.output_preview_player.load(video_path)
                self.output_preview_player.play()
                print(f"Loaded output preview: {os.path.basename(video_path)}")

            finally:
                self.output_video_load_lock.release()

        except Exception as e:
            print(f"Error loading output preview: {e}")

    def toggle_preview_playback(self, event=None):
        """Toggle latent preview playback"""
        try:
            if self.latent_preview_player and not self.using_gif_player:
                if self.latent_preview_player.is_paused():
                    self.latent_preview_player.play()
                else:
                    self.latent_preview_player.pause()
        except Exception as e:
            print(f"Error toggling latent preview playback: {e}")

    def toggle_output_preview_playback(self, event=None):
        """Toggle output preview playback"""
        try:
            if self.output_preview_player:
                if self.output_preview_player.is_paused():
                    self.output_preview_player.play()
                else:
                    self.output_preview_player.pause()
        except Exception as e:
            print(f"Error toggling output preview playback: {e}")

    def force_stop_all(self):
        """Force stop all processing"""
        try:
            # Create multiple stop flag files for comprehensive stopping
            current_dir = os.getcwd()

            # Main stop flag
            stop_flag_path = os.path.join(current_dir, "stop_framepack.flag")
            with open(stop_flag_path, 'w') as f:
                f.write(f"Force stop all requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Queue stop flag
            queue_stop_flag_path = os.path.join(current_dir, "stop_queue.flag")
            with open(queue_stop_flag_path, 'w') as f:
                f.write(f"Force stop queue requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Skip flag for good measure
            skip_flag_path = os.path.join(current_dir, "skip_generation.flag")
            with open(skip_flag_path, 'w') as f:
                f.write(f"Force skip requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            print("Force stop all flags created")
            messagebox.showinfo("Force Stop", "Force stop all signals sent. All processing will terminate.")

        except Exception as e:
            print(f"Error creating force stop flags: {e}")
            messagebox.showerror("Error", f"Failed to create stop flags: {e}")

    def stop_generation(self):
        """Stop the current generation"""
        try:
            current_dir = os.getcwd()
            stop_flag_path = os.path.join(current_dir, "stop_framepack.flag")

            with open(stop_flag_path, 'w') as f:
                f.write(f"Stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            print("Stop flag created")
            messagebox.showinfo("Stop", "Stop signal sent. Processing will stop after current frame.")

        except Exception as e:
            print(f"Error creating stop flag: {e}")
            messagebox.showerror("Error", f"Failed to create stop flag: {e}")

    def skip_generation(self):
        """Skip the current generation"""
        try:
            current_dir = os.getcwd()
            skip_flag_path = os.path.join(current_dir, "skip_generation.flag")

            with open(skip_flag_path, 'w') as f:
                f.write(f"Skip requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            print("Skip flag created")
            messagebox.showinfo("Skip", "Skip signal sent. Current generation will be skipped.")

        except Exception as e:
            print(f"Error creating skip flag: {e}")
            messagebox.showerror("Error", f"Failed to create skip flag: {e}")

    def reset_window_size(self, event=None):
        """Reset window to minimum size and restore functionality (F5 key)"""
        try:
            # Reset to minimum size
            self.root.geometry("600x800")
            self.root.minsize(400, 600)

            # Force update
            self.root.update_idletasks()

            print("Window size reset")

        except Exception as e:
            print(f"Error resetting window size: {e}")

    def on_closing(self):
        """Handle window closing"""
        try:
            # Stop any ongoing operations
            print("Closing preview window...")

            # Clean up video players
            if self.latent_preview_player:
                try:
                    if hasattr(self.latent_preview_player, 'stop'):
                        self.latent_preview_player.stop()
                except:
                    pass

            if self.output_preview_player:
                try:
                    if hasattr(self.output_preview_player, 'stop'):
                        self.output_preview_player.stop()
                except:
                    pass

            # Destroy window
            self.root.destroy()

        except Exception as e:
            print(f"Error during window closing: {e}")
            self.root.destroy()


def main():
    """Main function to run the standalone preview window"""
    root = tk.Tk()
    app = FramePackPreviewWindow(root)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Preview window interrupted by user")
    except Exception as e:
        print(f"Error running preview window: {e}")
    finally:
        try:
            root.destroy()
        except:
            pass


if __name__ == "__main__":
    main()