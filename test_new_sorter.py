#!/usr/bin/env python
"""
Test script for the new framepack_output_sorter.py functionality
"""

import sys
import os

# Add the current directory to the path so we can import framepack_output_sorter
sys.path.insert(0, '.')

from framepack_output_sorter import parse_filename, extract_file_info, group_files

def test_new_naming_scheme():
    """Test the new naming scheme parsing"""
    print("=== Testing New Naming Scheme ===")
    
    # Test files from your example
    test_files = [
        "20250714_231725_250715_051437_924_6958_109_15s.mp4",
        "20250714_231725_250715_051437_924_6958_end.png",
        "20250714_231725_250715_051437_924_6958_100.mp4",
        "20250714_231725_250715_051437_924_6958_91.mp4",
        "20250714_231725_250715_051437_924_6958_82.mp4",
        "20250714_231725_250715_051437_924_6958_73.mp4",
        "20250714_231725_250715_051437_924_6958_64.mp4",
        "20250714_231725_250715_051437_924_6958_55.mp4",
        "20250714_231725_250715_051437_924_6958_46.mp4",
        "20250714_231725_250715_051437_924_6958_37.mp4",
        "20250714_231725_250715_051437_924_6958_28.mp4",
        "20250714_231725_250715_051437_924_6958_19.mp4",
        "20250714_231725_250715_051437_924_6958_10.mp4",
        "20250714_231725_250715_051437_924_6958_start.png"
    ]
    
    expected_job_id = "20250714_231725_250715_051437_924_6958"
    
    print(f"Expected job ID: {expected_job_id}")
    print("\nTesting parse_filename:")
    
    for filename in test_files:
        result = parse_filename(filename)
        print(f"  {filename} -> {result}")
        
        if result:
            job_id, seed = result
            if job_id == expected_job_id:
                print(f"    ✅ Correct job ID")
            else:
                print(f"    ❌ Wrong job ID: expected {expected_job_id}, got {job_id}")
        else:
            print(f"    ❌ Failed to parse")
    
    print("\nTesting extract_file_info:")
    for filename in test_files:
        info = extract_file_info(filename)
        print(f"  {filename}:")
        print(f"    Job ID: {info.get('job_id')}")
        print(f"    Suffix: {info.get('suffix')}")
        print(f"    File Type: {info.get('file_type')}")
        print(f"    Duration: {info.get('duration')}")
        print()

def test_grouping():
    """Test file grouping"""
    print("=== Testing File Grouping ===")
    
    # Create fake file paths for testing
    test_files = [
        "20250714_231725_250715_051437_924_6958_109_15s.mp4",
        "20250714_231725_250715_051437_924_6958_100.mp4",
        "20250714_231725_250715_051437_924_6958_91.mp4",
        "20250714_231725_250715_051437_924_6958_end.png",
        "20250714_231725_250715_051437_924_6958_start.png"
    ]
    
    # Create fake paths (they don't need to exist for grouping test)
    fake_paths = [f"./{filename}" for filename in test_files]
    
    groups = group_files(fake_paths)
    
    print(f"Number of groups: {len(groups)}")
    for group_key, files in groups.items():
        job_id, seed = group_key
        print(f"Group: {job_id} (seed: {seed})")
        for file_path in files:
            filename = os.path.basename(file_path)
            print(f"  - {filename}")

def test_legacy_naming_scheme():
    """Test legacy naming scheme still works"""
    print("=== Testing Legacy Naming Scheme ===")
    
    legacy_files = [
        "250420_121919_242_3623_37.mp4",
        "250420_121919_242_3623_37_24s.mp4",
        "250426_085120_706_7278_19_seed357798872.mp4",
        "image_name_5s.mp4",
        "image_name_seed123456_5s.mp4"
    ]
    
    print("Testing parse_filename for legacy files:")
    for filename in legacy_files:
        result = parse_filename(filename)
        print(f"  {filename} -> {result}")

if __name__ == "__main__":
    test_new_naming_scheme()
    print("\n" + "="*60 + "\n")
    test_grouping()
    print("\n" + "="*60 + "\n")
    test_legacy_naming_scheme()
