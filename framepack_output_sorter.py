#!/usr/bin/env python
"""
framepack_output_sorter.py

This script scans the current directory for MP4 files,
groups them based on job ID patterns in their filenames, and copies only the
largest file from the newest files in each job group to a 'sorted' subfolder.

NEW NAMING SCHEME (Primary):
- YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_ZZZ_##s.mp4 (video with duration)
- YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_###.mp4 (video without duration)

Note: PNG files (_end.png, _start.png) are recognized for job ID grouping but not sorted.

LEGACY NAMING SCHEME (Still supported):
- DATE_TIME_XXX_YYYY(_ZZ)(_seed#######)(_XXs).mp4
- image_name(_seed#######)(_XXs).mp4

Files are grouped by their job ID (first 6 parts of new naming scheme).
For new naming scheme: Prioritizes files with duration suffix (_##s), then highest suffix number.
For legacy naming scheme: Falls back to longest video duration.

The script selects the newest file from each job group, then copies only the
largest file among all selected newest files. This ensures only one file per
batch is copied and avoids file collisions.

IMPORTANT: This script preserves all original files in the current directory.
It copies files to a 'sorted' subfolder without deleting any files.
"""

import os
import re
import shutil
import subprocess
import argparse
import sys
import traceback
from collections import defaultdict


def parse_filename(filename):
    """
    Parse a filename to extract its base group identifier (job ID) and seed value.

    New naming scheme examples:
    - 20250714_231725_250715_051437_924_6958_109_15s.mp4 -> (20250714_231725_250715_051437_924_6958, None)
    - 20250714_231725_250715_051437_924_6958_end.png -> (20250714_231725_250715_051437_924_6958, None)
    - 20250714_231725_250715_051437_924_6958_100.mp4 -> (20250714_231725_250715_051437_924_6958, None)
    - 20250714_231725_250715_051437_924_6958_start.png -> (20250714_231725_250715_051437_924_6958, None)

    Legacy naming scheme examples (still supported):
    - 250420_121919_242_3623_37.mp4 -> (250420_121919_242_3623, None)
    - 250420_121919_242_3623_37_24s.mp4 -> (250420_121919_242_3623, None)
    - 250426_085120_706_7278_19_seed357798872.mp4 -> (250426_085120_706_7278, 357798872)
    - image_name_5s.mp4 -> (image_name, None)
    - image_name_seed123456_5s.mp4 -> (image_name, 123456)

    Returns a tuple of (job_id, seed) or None if the filename doesn't match any pattern.
    """
    # Only match MP4 files for sorting (PNG files are recognized but not sorted)
    if not filename.lower().endswith('.mp4'):
        return None

    # NEW NAMING SCHEME: YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_ZZZ_##s.mp4
    # or YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_end.png
    # or YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_start.png
    # Pattern: Extract the job ID (first 6 parts) as the grouping identifier
    new_pattern = r'^(\d{8}_\d{6}_\d{6}_\d{6}_\d+_\d+)_(?:\d+_\d+s|end|start|\d+)\.(?:mp4|png)$'
    match = re.match(new_pattern, filename)

    if match:
        job_id = match.group(1)
        return (job_id, None)  # No seed extraction for new naming scheme

    # LEGACY NAMING SCHEME SUPPORT
    # Pattern matches various legacy formats:
    # 1. DATE_TIME_XXX_YYYY(_ZZ).mp4
    # 2. DATE_TIME_XXX_YYYY(_ZZ_seed#######).mp4
    # 3. DATE_TIME_XXX_YYYY(_###).mp4
    # 4. DATE_TIME_XXX_YYYY(_###_seed#######).mp4
    # 5. Any of the above with _XXs.mp4 suffix (where XX is duration in seconds)
    # 6. image_name(_seed#######)(_XXs).mp4 - For non-timestamp filenames

    # First try legacy timestamp pattern
    legacy_pattern = r'^(\d+_\d+_\d+_\d+)(?:_\d+)?(?:_seed(\d+))?(?:_\d+s)?\.mp4$'
    match = re.match(legacy_pattern, filename)

    if match:
        base_id = match.group(1)
        seed = match.group(2) if match.group(2) else None
        return (base_id, seed)

    # If legacy timestamp pattern doesn't match, try generic filename pattern
    # This handles filenames like "image_name_seed123456_5s.mp4"
    legacy_pattern2 = r'^(.+?)(?:_seed(\d+))?(?:_\d+s)?\.mp4$'
    match2 = re.match(legacy_pattern2, filename)

    if match2:
        base_id = match2.group(1)
        seed = match2.group(2) if match2.group(2) else None
        return (base_id, seed)

    return None


def extract_file_info(filename):
    """
    Extract detailed information from a filename.
    Returns a dictionary with extracted information.
    """
    info = {
        "filename": filename,
        "job_id": None,
        "suffix": None,
        "seed": None,
        "duration": None,
        "file_type": None,
        "creation_timestamp": None
    }

    # NEW NAMING SCHEME: Extract job_id, suffix, and duration
    # Pattern: YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_ZZZ_##s.mp4
    # or YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_end.png
    # or YYYYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_start.png
    new_match = re.match(r'^(\d{8}_\d{6}_\d{6}_\d{6}_\d+_\d+)_(\d+)_(\d+)s\.mp4$', filename)
    if new_match:
        info["job_id"] = new_match.group(1)
        info["suffix"] = new_match.group(2)
        info["duration"] = new_match.group(3)
        info["file_type"] = "video_with_duration"
        # Extract creation timestamp from job_id (first part: YYYYMMDD_HHMMSS)
        timestamp_match = re.match(r'^(\d{8}_\d{6})', info["job_id"])
        if timestamp_match:
            info["creation_timestamp"] = timestamp_match.group(1)
        return info

    # Check for new naming scheme without duration (just number suffix)
    new_match2 = re.match(r'^(\d{8}_\d{6}_\d{6}_\d{6}_\d+_\d+)_(\d+)\.mp4$', filename)
    if new_match2:
        info["job_id"] = new_match2.group(1)
        info["suffix"] = new_match2.group(2)
        info["file_type"] = "video_no_duration"
        # Extract creation timestamp from job_id (first part: YYYYMMDD_HHMMSS)
        timestamp_match = re.match(r'^(\d{8}_\d{6})', info["job_id"])
        if timestamp_match:
            info["creation_timestamp"] = timestamp_match.group(1)
        return info

    # Check for new naming scheme PNG files (end/start)
    new_match3 = re.match(r'^(\d{8}_\d{6}_\d{6}_\d{6}_\d+_\d+)_(end|start)\.png$', filename)
    if new_match3:
        info["job_id"] = new_match3.group(1)
        info["suffix"] = new_match3.group(2)
        info["file_type"] = "image"
        # Extract creation timestamp from job_id (first part: YYYYMMDD_HHMMSS)
        timestamp_match = re.match(r'^(\d{8}_\d{6})', info["job_id"])
        if timestamp_match:
            info["creation_timestamp"] = timestamp_match.group(1)
        return info

    # LEGACY NAMING SCHEME SUPPORT
    # First try legacy timestamp pattern
    legacy_match = re.match(r'^(\d+_\d+_\d+_\d+)(?:_(\d+))?(?:_seed(\d+))?(?:_(\d+)s)?\.mp4$', filename)
    if legacy_match:
        info["job_id"] = legacy_match.group(1)  # Use job_id instead of base_id for consistency
        info["suffix"] = legacy_match.group(2) if legacy_match.group(2) else ""
        info["seed"] = legacy_match.group(3) if legacy_match.group(3) else ""
        info["duration"] = legacy_match.group(4) if legacy_match.group(4) else ""
        info["file_type"] = "legacy_video"
        return info

    # If legacy timestamp pattern doesn't match, try generic filename pattern
    # This handles filenames like "image_name_seed123456_5s.mp4"
    legacy_match2 = re.match(r'^(.+?)(?:_seed(\d+))?(?:_(\d+)s)?\.mp4$', filename)
    if legacy_match2:
        info["job_id"] = legacy_match2.group(1)  # Use job_id instead of base_id for consistency
        info["suffix"] = ""  # No suffix in this pattern
        info["seed"] = legacy_match2.group(2) if legacy_match2.group(2) else ""
        info["duration"] = legacy_match2.group(3) if legacy_match2.group(3) else ""
        info["file_type"] = "legacy_generic"

    return info


def group_files(file_paths):
    """
    Group files based on their base identifier and seed value.
    Returns a dictionary where keys are (base_id, seed) tuples and values are lists of file paths.
    Files with the same base_id and seed are considered duplicates.
    """
    groups = defaultdict(list)

    for file_path in file_paths:
        if not os.path.isfile(file_path):
            continue

        filename = os.path.basename(file_path)
        result = parse_filename(filename)

        if result:
            base_id, seed = result
            # Use a tuple of (base_id, seed) as the key
            # This groups files with the same base_id and seed together
            group_key = (base_id, seed)
            groups[group_key].append(file_path)

    return groups


def get_largest_file(file_paths):
    """
    Return the path to the largest file in the given list.
    Includes detailed logging to help diagnose size comparison issues.
    """
    if not file_paths:
        return None

    # Get file sizes and sort by size (largest first)
    files_with_sizes = [(path, os.path.getsize(path)) for path in file_paths]

    # Sort by size in descending order
    sorted_files = sorted(files_with_sizes, key=lambda x: x[1], reverse=True)

    # Print detailed information about each file
    print("  Detailed file size comparison:")
    for path, size in sorted_files:
        filename = os.path.basename(path)
        has_seconds = "_s.mp4" in filename.lower()
        print(f"    {filename} - {size} bytes - {'Has seconds indicator' if has_seconds else 'No seconds indicator'}")

    # Return the path of the largest file
    largest_file = sorted_files[0][0]
    largest_filename = os.path.basename(largest_file)
    print(f"  Selected largest file: {largest_filename} - {sorted_files[0][1]} bytes")

    return largest_file


def get_video_duration(file_path):
    """
    Get the duration of a video file in seconds using FFmpeg.

    Args:
        file_path: Path to the video file

    Returns:
        The duration in seconds as a float, or 0 if the duration couldn't be determined
    """
    try:
        # Use FFmpeg to get the duration
        cmd = ['ffmpeg', '-i', file_path, '-hide_banner']
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        # Parse the output to find the duration
        duration_match = re.search(r'Duration: (\d+):(\d+):(\d+\.\d+)', result.stderr)
        if duration_match:
            hours = int(duration_match.group(1))
            minutes = int(duration_match.group(2))
            seconds = float(duration_match.group(3))
            total_seconds = hours * 3600 + minutes * 60 + seconds
            return total_seconds

        # If we couldn't find the duration, try another method
        cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        if result.stdout.strip():
            return float(result.stdout.strip())

        # If we still couldn't find the duration, return 0
        return 0
    except Exception as e:
        print(f"  ⚠️ Error getting duration for {os.path.basename(file_path)}: {str(e)}")
        return 0


def get_newest_file(file_paths):
    """
    Return the path to the newest file in the given list based on the naming scheme.

    For new naming scheme: Prioritizes files with duration suffix (_##s), then highest suffix number.
    For legacy naming scheme: Falls back to longest video duration.

    Args:
        file_paths: List of paths to video/image files

    Returns:
        The path to the newest file, or None if no files were provided
    """
    if not file_paths:
        return None

    # Analyze files and extract information
    files_with_info = []
    has_new_naming_scheme = False

    for path in file_paths:
        filename = os.path.basename(path)
        size = os.path.getsize(path)
        info = extract_file_info(filename)

        # Check if this is using the new naming scheme
        if info and info.get("file_type") in ["video_with_duration", "video_no_duration", "image"]:
            has_new_naming_scheme = True

        # Get video duration for MP4 files
        duration = 0
        if filename.lower().endswith('.mp4'):
            duration = get_video_duration(path)

        files_with_info.append((path, info, duration, size, filename))

    # Sort based on naming scheme
    if has_new_naming_scheme:
        print("  Using new naming scheme logic - selecting newest file by suffix priority:")

        # For new naming scheme: prioritize files with duration suffix, then by highest suffix number
        def sort_key_new(item):
            path, info, duration, size, filename = item

            if not info or not info.get("job_id"):
                return (0, 0, 0)  # Put unrecognized files at the end

            # Priority 1: Files with duration suffix (_##s) get highest priority
            has_duration_suffix = info.get("file_type") == "video_with_duration"
            priority = 2 if has_duration_suffix else 1

            # Priority 2: Higher suffix numbers are newer
            suffix = info.get("suffix", "")
            try:
                suffix_num = int(suffix) if suffix and suffix.isdigit() else 0
            except:
                suffix_num = 0

            # Priority 3: File size as tiebreaker
            return (priority, suffix_num, size)

        sorted_files = sorted(files_with_info, key=sort_key_new, reverse=True)

        # Print detailed information about each file
        for path, info, duration, size, filename in sorted_files:
            file_type = info.get("file_type", "unknown") if info else "unknown"
            suffix = info.get("suffix", "") if info else ""
            duration_str = info.get("duration", "") if info else ""

            type_info = f"Type: {file_type}"
            suffix_info = f"Suffix: {suffix}" if suffix else "No suffix"
            duration_info = f"Duration: {duration_str}s" if duration_str else f"Video duration: {duration:.2f}s" if duration > 0 else ""

            print(f"    {filename} - {type_info} - {suffix_info} - {duration_info} - {size} bytes")

        # Return the newest file
        newest_file = sorted_files[0][0]
        newest_filename = os.path.basename(newest_file)
        newest_info = sorted_files[0][1]
        print(f"  Selected newest file: {newest_filename} - Priority: {sort_key_new(sorted_files[0])}")

    else:
        print("  Using legacy naming scheme logic - selecting longest file by duration:")

        # For legacy naming scheme: use duration-based selection
        sorted_files = sorted(files_with_info, key=lambda x: (x[2], x[3]), reverse=True)

        # Print detailed information about each file
        for path, info, duration, size, filename in sorted_files:
            has_seconds = bool(re.search(r'_\d+s\.mp4$', filename.lower()))
            seconds_info = f" - Has seconds indicator" if has_seconds else ""
            print(f"    {filename} - {duration:.2f} seconds - {size} bytes{seconds_info}")

        # Return the longest file
        newest_file = sorted_files[0][0]
        newest_filename = os.path.basename(newest_file)
        print(f"  Selected longest file: {newest_filename} - {sorted_files[0][2]:.2f} seconds - {sorted_files[0][3]} bytes")

    return newest_file


def get_longest_file(file_paths):
    """
    Legacy function - now redirects to get_newest_file for consistency.
    """
    return get_newest_file(file_paths)


def process_files(directory=".", use_size=False):
    """
    Process files in the specified directory:
    1. Group them based on job ID patterns
    2. For each group, find the newest file (prioritizing files with duration suffix)
    3. Copy only the largest file from the selected newest files to the 'sorted' subfolder
    4. Avoid file collisions - only copy if destination doesn't exist

    Args:
        directory: Directory to process files in
        use_size: If True, use file size instead of newest file logic (legacy mode)

    Returns:
        Number of files copied to the sorted directory
    """
    print(f"\n=== Starting Automatic Sorting ===")
    print(f"Processing files in: {directory}")
    print(f"Selection method: {'File size (legacy)' if use_size else 'Newest file by naming scheme'}")

    # Create the sorted directory if it doesn't exist
    sorted_dir = os.path.join(directory, "sorted")
    os.makedirs(sorted_dir, exist_ok=True)

    # Get all MP4 files in the directory (excluding the sorted directory and script files)
    file_paths = []
    for file in os.listdir(directory):
        # Skip the sorted directory and script files
        if file == "sorted" or file.startswith("framepack_"):
            continue

        # Process only MP4 files for sorting
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(directory, file)
        if os.path.isfile(full_path):
            file_paths.append(full_path)

    # Check if we found any files to process
    if not file_paths:
        print("No MP4 files found to process in the directory.")
        return 0

    print(f"Found {len(file_paths)} MP4 files to process")

    # Group the files by job ID
    groups = group_files(file_paths)

    # Check if we have any groups after parsing
    if not groups:
        print("No valid file groups found. Check if filenames match the expected pattern.")
        return 0

    print(f"Grouped into {len(groups)} distinct job groups")

    # Print detailed information about the grouping
    print(f"\nDetailed grouping information:")
    for group_key, files_in_group in groups.items():
        job_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"  Job Group: {job_id}{seed_info} - {len(files_in_group)} files")
        for file_path in files_in_group:
            filename = os.path.basename(file_path)
            info = extract_file_info(filename)
            file_type = info.get("file_type", "unknown") if info else "unknown"
            suffix = info.get("suffix", "") if info else ""
            print(f"    {filename} - Type: {file_type} - Suffix: {suffix}")

    # Process each group to find the newest file, then select the largest among newest files
    newest_files = []

    print("\nProcessing job groups to find newest files:")
    for group_key, files_in_group in groups.items():
        job_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"\nJob Group: {job_id}{seed_info} - {len(files_in_group)} files")

        # Get the newest file from this group
        if use_size:
            newest_file = get_largest_file(files_in_group)
            print(f"  Using largest file by size (legacy mode)")
        else:
            newest_file = get_newest_file(files_in_group)
            print(f"  Using newest file by naming scheme")

        if newest_file:
            newest_files.append(newest_file)

    # Now select only the largest file among all the newest files
    if not newest_files:
        print("No newest files found to process.")
        return 0

    print(f"\nFound {len(newest_files)} newest files from job groups")
    print("Selecting the largest file among newest files:")

    # Get file sizes for all newest files
    files_with_sizes = []
    for file_path in newest_files:
        filename = os.path.basename(file_path)
        size = os.path.getsize(file_path)
        files_with_sizes.append((file_path, size, filename))
        print(f"  {filename} - {size} bytes")

    # Sort by size (largest first) and select the largest
    files_with_sizes.sort(key=lambda x: x[1], reverse=True)
    largest_newest_file = files_with_sizes[0][0]
    largest_filename = files_with_sizes[0][2]
    largest_size = files_with_sizes[0][1]

    print(f"\nSelected largest file: {largest_filename} - {largest_size} bytes")

    # Copy only the largest file to sorted directory
    copied_files = []
    skipped_files = []

    dest_filename = os.path.basename(largest_newest_file)
    dest_path = os.path.join(sorted_dir, dest_filename)

    if os.path.exists(dest_path):
        skipped_files.append(dest_filename)
        print(f"Skipped copying {dest_filename} (already exists in destination - no collision allowed)")
    else:
        shutil.copy2(largest_newest_file, dest_path)
        copied_files.append(dest_filename)
        print(f"  Copied {dest_filename} to sorted directory")

    # Print summary
    print(f"\nProcessed {len(groups)} job groups containing {len(file_paths)} total files.")
    print(f"Selected {len(newest_files)} newest files from job groups.")
    print(f"Copied {len(copied_files)} largest file to {sorted_dir}")

    if copied_files:
        print("\nFile copied:")
        for file in copied_files:
            print(f"  - {file}")

    if skipped_files:
        print(f"\nSkipped {len(skipped_files)} file (already exists in destination - no collision allowed):")
        for file in skipped_files:
            print(f"  - {file}")

    print("\n=== Automatic Sorting Completed ===")
    return len(copied_files)


def check_non_matching_files(directory="."):
    """
    Check for files that don't match the expected naming schemes.
    Specifically looks for MP4 files without the seconds indicator.

    Args:
        directory: Directory to process files in

    Returns:
        Tuple containing:
        - List of files that don't match any naming scheme
        - List of files that match a naming scheme but don't have seconds indicator
    """
    print(f"\n=== Checking for Non-Matching Files ===")
    print(f"Scanning directory: {directory}")

    # Get all MP4 files in the directory (excluding the sorted directory and script files)
    file_paths = []
    for file in os.listdir(directory):
        # Skip the sorted directory and script files
        if file == "sorted" or file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(directory, file)
        if os.path.isfile(full_path):
            file_paths.append(full_path)

    # Check if we found any files to process
    if not file_paths:
        print("No MP4 files found to check in the directory.")
        return [], []

    print(f"Found {len(file_paths)} MP4 files to check")

    # Lists to store results
    non_matching_files = []
    missing_seconds_files = []

    # Check each file
    for file_path in file_paths:
        filename = os.path.basename(file_path)

        # Check if the file matches any naming scheme
        result = parse_filename(filename)

        if not result:
            # File doesn't match any naming scheme
            non_matching_files.append(file_path)
            continue

        # Check if the file has a seconds indicator
        has_seconds = bool(re.search(r'_\d+s\.mp4$', filename.lower()))
        if not has_seconds:
            missing_seconds_files.append(file_path)

    # Print results
    if non_matching_files:
        print(f"\nFound {len(non_matching_files)} files that don't match any naming scheme:")
        for file_path in non_matching_files:
            print(f"  - {os.path.basename(file_path)}")
    else:
        print("\nAll files match a valid naming scheme.")

    if missing_seconds_files:
        print(f"\nFound {len(missing_seconds_files)} files that match a naming scheme but don't have seconds indicator:")
        for file_path in missing_seconds_files:
            print(f"  - {os.path.basename(file_path)}")
    else:
        print("\nAll valid files have seconds indicators.")

    print("\n=== File Check Completed ===")
    return non_matching_files, missing_seconds_files


def main():
    """
    Main function to parse arguments and process files.
    """
    parser = argparse.ArgumentParser(
        description="Sort FramePack output files by job ID, selecting the newest file from each job group, then copying only the largest file among all newest files",
        epilog="This script groups files by job ID, finds the newest file in each group (prioritizing files with duration suffix), then copies only the largest file among all selected newest files to avoid collisions."
    )
    parser.add_argument("--directory", "-d", type=str, default=".",
                        help="Directory to process files in (default: current directory)")
    parser.add_argument("--use-size", "-s", action="store_true",
                        help="Use legacy mode: select largest file by size instead of newest file logic")
    parser.add_argument("--check-only", "-c", action="store_true",
                        help="Only check for files that don't match naming schemes, don't perform sorting")

    args = parser.parse_args()

    print("Framepack Output Sorter")
    print("======================")
    print(f"Scanning directory: {args.directory}")

    try:
        if args.check_only:
            # Run in check-only mode
            print("Running in check-only mode (no files will be sorted)")
            non_matching, missing_seconds = check_non_matching_files(args.directory)

            # Print summary
            total_issues = len(non_matching) + len(missing_seconds)
            if total_issues > 0:
                print(f"\nFound {total_issues} files with naming issues.")
                print("Consider using framepack_add_seconds_label.py to add seconds indicators to files.")
            else:
                print("\nNo issues found with file naming conventions.")
        else:
            # Process files in the specified directory
            num_copied = process_files(args.directory, args.use_size)

            if num_copied > 0:
                print("\nOperation completed successfully!")
            else:
                print("\nNo new files were copied.")
    except Exception as e:
        print(f"\nError: {str(e)}")
        traceback.print_exc()
        return 1

    # Keep console window open if running directly
    if sys.stdout.isatty():
        input("\nPress Enter to exit...")

    return 0


if __name__ == "__main__":
    sys.exit(main())
