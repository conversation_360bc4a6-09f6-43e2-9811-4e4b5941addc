@echo off
setlocal enabledelayedexpansion

:: FramePack Output Sorter Batch File
:: This batch file provides a user-friendly interface to run the framepack_output_sorter.py script

title FramePack Output Sorter

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python is not installed or not in the PATH.
    echo Please install Python and make sure it's in your PATH.
    pause
    exit /b 1
)

:: Check if the sorter script exists
if not exist framepack_output_sorter.py (
    echo ERROR: framepack_output_sorter.py not found in the current directory.
    echo Please make sure you're running this batch file from the same directory as the script.
    pause
    exit /b 1
)

:: Check if FFmpeg is available (needed for video duration detection)
ffmpeg -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: FFmpeg is not installed or not in the PATH.
    echo Without FFmpeg, the sorter will fall back to using file size instead of video duration.
    echo Press any key to continue anyway...
    pause >nul
)

:: Default values
set "directory=."
set "use_size="
set "check_only="

:menu
cls
echo ===================================================
echo              FRAMEPACK OUTPUT SORTER
echo ===================================================
echo.
echo This tool groups MP4/PNG files by job ID, finds the newest
echo file from each job group, then copies only the LARGEST file
echo among all newest files to avoid collisions.
echo.
echo Current Settings:
echo   Directory to scan: %directory%
echo   Selection method: !use_size!
echo   Check only mode: !check_only!
echo.
echo Options:
echo   1. Set directory to scan (current: %directory%)
echo   2. Toggle selection method (current: !use_size!)
echo   3. Toggle check only mode (current: !check_only!)
echo   4. Run sorter with current settings
echo   5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto set_directory
if "%choice%"=="2" goto toggle_method
if "%choice%"=="3" goto toggle_check_only
if "%choice%"=="4" goto run_sorter
if "%choice%"=="5" goto end
goto menu

:set_directory
cls
echo ===================================================
echo                SET DIRECTORY TO SCAN
echo ===================================================
echo.
echo Enter the path to the directory you want to scan.
echo Default is the current directory.
echo.
set /p directory="Directory path: "
if "!directory!"=="" set "directory=."
goto menu

:toggle_method
if "!use_size!"=="Use file size" (
    set "use_size=Use video duration"
    set "size_flag="
) else (
    set "use_size=Use file size"
    set "size_flag=--use-size"
)
goto menu

:toggle_check_only
if "!check_only!"=="Enabled" (
    set "check_only="
    set "check_flag="
) else (
    set "check_only=Enabled"
    set "check_flag=--check-only"
)
goto menu

:run_sorter
cls
echo ===================================================
echo               RUNNING OUTPUT SORTER
echo ===================================================
echo.
echo Running with the following settings:
echo   Directory: %directory%
if "!use_size!"=="Use file size" (
    echo   Selection method: Using file size
) else (
    echo   Selection method: Using video duration (default)
)
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

:: Build the command
set "cmd=python framepack_output_sorter.py --directory "%directory%""
if "!use_size!"=="Use file size" set "cmd=!cmd! --use-size"
if "!check_only!"=="Enabled" set "cmd=!cmd! --check-only"

:: Display check only mode info if enabled
if "!check_only!"=="Enabled" (
    echo   Check only mode: Enabled (will only check for non-matching filenames)
)

echo.
echo Running command: !cmd!
echo.
echo ===================================================
echo                  SORTER OUTPUT
echo ===================================================
echo.

:: Run the command
!cmd!

echo.
echo ===================================================
echo                  SORTER FINISHED
echo ===================================================
echo.
echo Press any key to return to the menu...
pause >nul
goto menu

:end
echo.
echo Thank you for using FramePack Output Sorter.
echo.
endlocal
exit /b 0
