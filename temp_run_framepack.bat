@echo off
setlocal enabledelayedexpansion
title FramePack Batch Processing
color 0A
echo ===============================================
echo             FRAMEPACK BATCH PROCESSING         
echo ===============================================
echo.
echo Running FramePack with the following settings:
echo.
echo Queue Job: 1/1
echo Iteration: 1/2
echo.
echo Mode:                Combined (Files and URLs)
echo Number of Items:     18 (18 files, 0 URLs)
echo File 1:             magicquill_df048dbe5017b247c221590c37b9b3db33dfc058b8fcfae4d85412b0dcea8e7a.png
echo File 2:             magicquill_fe4f09d173ec8ee5e4f09cd08d7dee6a260c238a23a77fd879cecd39b80cd5fe.png
echo File 3:             magicquill_ec2da766540142572f372d9093bd6b6ddd8dd56f35d3327d3d15a2b9105ba7c0.png
echo File 4:             magicquill_c6e1c0c367bdcc89769d76a424528d2de9a25c425b72f0d0b0073c976fc036f7.png
echo File 5:             magicquill_b848a7d19726bb25f6d57c243858aec54ea291e146a5c60e495e4044647e961a.png
echo                    ... and 13 more items
echo Output Directory:   A:\AI\FramePack\outputs
echo Fallback Prompt:    A character doing some simple body movements.
echo Model Type:         FramePack F1
echo Seed:               -1 (Random)
echo Video Length:       30.0 seconds
echo Steps:              25
echo Distilled CFG:      10.0
echo GPU Memory:         6.0 GB
echo MP4 Compression:    16 (0-51, lower is better)
echo Latent Window Size: 9
echo Use TeaCache:       Yes
echo Use Image Prompt:   No
echo Use Noise (T2V):    No
echo Use Prompt List:    No
echo Fix Encoding:       Yes (always enabled)
echo Overwrite Existing: No
echo Allow Duplicates:   Yes
echo Apply All Prompts:  No
echo LoRA Files:         (None - Disabled)
echo FP8 Optimization:  No
echo.
echo ===============================================
echo.
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %ERRORLEVEL% NEQ 0 (
    color 0C
    echo ERROR: Failed to activate virtual environment.
    echo Please make sure the venv directory exists and is properly set up.
    echo.
    pause
    exit /b 1
)
echo Virtual environment activated successfully.
echo.
echo Running command: A:\AI\FramePack\venv\Scripts\python.exe batch_f1_lock.py --unified-list "temp_combined_list.txt" --output_dir "A:\AI\FramePack\outputs" --prompt "A character doing some simple body movements." --seed -1 --video_length 30.0 --steps 25 --distilled_cfg 10.0 --flow_shift 0.0 --gpu_memory 6.0 --mp4_crf 16 --latent_window_size 9 --use_teacache --randomize_order --filter_black_transparent --no_image_prompt --allow_duplicates --fix_encoding --append_lora_keywords --job_id 20250715_170633
echo.
REM Check if the temp_combined_list.txt file exists
if not exist "temp_combined_list.txt" (
    echo Error: temp_combined_list.txt file not found
    echo Creating empty file to prevent further errors
    echo. > temp_combined_list.txt
)
echo.
REM Run the command with error handling
set COMMAND_RESULT=0
echo Running command with real-time output...
call A:\AI\FramePack\venv\Scripts\python.exe batch_f1_lock.py --unified-list "temp_combined_list.txt" --output_dir "A:\AI\FramePack\outputs" --prompt "A character doing some simple body movements." --seed -1 --video_length 30.0 --steps 25 --distilled_cfg 10.0 --flow_shift 0.0 --gpu_memory 6.0 --mp4_crf 16 --latent_window_size 9 --use_teacache --randomize_order --filter_black_transparent --no_image_prompt --allow_duplicates --fix_encoding --append_lora_keywords --job_id 20250715_170633
set COMMAND_RESULT=%ERRORLEVEL%
echo Command completed with exit code: %COMMAND_RESULT%
echo Creating error detection log...
echo Command result: %COMMAND_RESULT% > temp_command_result.txt
REM Check for specific error conditions
set MISSING_FILES=0
if %COMMAND_RESULT% NEQ 0 (
    REM Check if this is likely a missing files error
    if exist "temp_file_list.txt" (
        echo Checking temp file list content...
        find /c /v "" temp_file_list.txt > temp_line_count.txt 2>nul
        if exist temp_line_count.txt (
            set /p LINE_COUNT=<temp_line_count.txt
            if "!LINE_COUNT!"=="0" set MISSING_FILES=1
            del temp_line_count.txt 2>nul
        ) else (
            echo Warning: Could not create line count file
            set MISSING_FILES=1
        )
    ) else (
        echo Temp file list not found, assuming missing files error...
        set MISSING_FILES=1
    )
)
echo.
if %MISSING_FILES% EQU 1 (
    color 0E
    echo ===============================================
    echo WARNING: Missing files or file list detected
    echo ===============================================
    echo.
    echo This is likely due to a temporary file being deleted too early.
    echo The queue will continue to the next item.
    echo.
    echo Creating completion signal file...
    echo MISSING_FILES > framepack_completed.signal
    echo Creating stop flag file...
    echo "Auto-stop after missing files at %DATE% %TIME%" > stop_framepack.flag
    echo Window will close in 5 seconds...
    timeout /t 5 > nul
) else if %COMMAND_RESULT% NEQ 0 (
    color 0C
    echo ===============================================
    echo ERROR: Processing failed with error code %COMMAND_RESULT%
    echo ===============================================
    echo.
    echo Please check the error messages above for details.
    echo.
    echo Creating completion signal file...
    echo ERROR > framepack_completed.signal
    echo Creating stop flag file...
    echo "Auto-stop after error at %DATE% %TIME%" > stop_framepack.flag
    echo Window will close in 10 seconds...
    timeout /t 10 > nul
) else (
    echo ===============================================
    echo Processing completed successfully!
    echo ===============================================
    echo.
    echo Creating completion signal file...
    echo SUCCESS > framepack_completed.signal
    echo Creating stop flag file...
    echo "Auto-stop after success at %DATE% %TIME%" > stop_framepack.flag
    echo Window will close in 10 seconds...
    timeout /t 10 > nul
)

REM Clean up the temporary files
echo Cleaning up temporary files...
if exist "temp_command_output.txt" del "temp_command_output.txt" 2>nul
if exist "temp_command_result.txt" del "temp_command_result.txt" 2>nul
if exist "temp_line_count.txt" del "temp_line_count.txt" 2>nul
REM Final check to ensure a completion signal file exists
if not exist "framepack_completed.signal" (
    echo Creating completion signal file as a fallback...
    echo FALLBACK > framepack_completed.signal
    echo Creating stop flag file...
    echo "Auto-stop fallback at %DATE% %TIME%" > stop_framepack.flag
)

REM Write command result for error checking
echo Command result: %COMMAND_RESULT% > temp_command_result.txt
if %COMMAND_RESULT% neq 0 (
    echo ERROR: Command failed with exit code %COMMAND_RESULT%
    echo Command failed at %DATE% %TIME% >> temp_command_result.txt
)

REM Add a longer delay to ensure the completion signal file is detected by the GUI
echo Waiting for GUI to detect completion...
timeout /t 10 > nul

REM Clean up the batch file itself after GUI has had time to detect completion
REM (goto) 2>nul & del "%~f0"
