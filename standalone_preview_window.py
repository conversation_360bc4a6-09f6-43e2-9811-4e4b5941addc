"""
Standalone Preview Window for FramePack
Contains only the third column preview components: image preview, latent animation preview, and output video preview
with auto-resizing panes and control buttons (Force Stop All, Stop, Skip)
"""

import os
import time
import threading
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import configparser

# Import MoviePy player
try:
    from moviepy_player import MoviePyPlayer
    MOVIEPY_AVAILABLE = True
    print("MoviePyPlayer loaded successfully for standalone preview window.")
except ImportError:
    print("MoviePyPlayer not available. Video preview will be disabled.")
    MOVIEPY_AVAILABLE = False
except Exception as e:
    print(f"Error importing MoviePyPlayer: {e}")
    MOVIEPY_AVAILABLE = False


class StandalonePreviewWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Preview Window")
        self.root.geometry("800x900")
        self.root.resizable(True, True)
        self.root.minsize(400, 500)
        
        # Initialize preview state tracking
        self.preview_state = {
            'latent_loading': False,
            'output_loading': False,
            'image_loading': False,
            'current_latent': None,
            'current_output': None,
            'current_image': None,
        }
        
        # Initialize video player variables
        self.latent_preview_player = None
        self.output_preview_player = None
        self.image_preview_photo = None
        self.current_image_preview = None
        self.current_preview = None
        self.current_output_preview = None
        
        # Create the UI
        self.create_ui()
        
        # Start preview monitoring
        if MOVIEPY_AVAILABLE:
            self.root.after(1000, self.update_previews)
        
        # Load window state
        self.load_window_state()
        
        # Bind window close event to save state
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Bind resize events
        self.root.bind('<Configure>', self.on_window_resize)
    
    def create_ui(self):
        """Create the user interface"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons frame at the top
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Control buttons
        self.force_stop_all_button = ttk.Button(
            control_frame,
            text="Force Stop All",
            command=self.force_stop_all,
            width=15
        )
        self.force_stop_all_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(
            control_frame,
            text="Stop",
            command=self.stop_generation,
            width=10
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.skip_button = ttk.Button(
            control_frame,
            text="Skip",
            command=self.skip_generation,
            width=10
        )
        self.skip_button.pack(side=tk.LEFT, padx=5)
        
        # Create main vertical paned window for previews
        self.main_preview_paned_window = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
        self.main_preview_paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Configure sash to be easier to grab
        try:
            self.main_preview_paned_window.configure(sashwidth=8)
        except Exception as e:
            print(f"Could not configure main preview paned window sash: {e}")
        
        # Create top previews frame (image and latent side by side)
        top_previews_frame = ttk.Frame(self.main_preview_paned_window)
        self.main_preview_paned_window.add(top_previews_frame, weight=1)
        
        # Create horizontal paned window for image and latent previews
        self.preview_paned_window = ttk.PanedWindow(top_previews_frame, orient=tk.HORIZONTAL)
        self.preview_paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Configure sash
        try:
            self.preview_paned_window.configure(sashwidth=8)
        except Exception as e:
            print(f"Could not configure preview paned window sash: {e}")
        
        # Create image preview frame (left side)
        self.create_image_preview_frame()
        
        # Create latent preview frame (right side)
        self.create_latent_preview_frame()
        
        # Create output video preview frame (bottom)
        self.create_output_preview_frame()
        
        # Bind resize events
        self.main_preview_paned_window.bind('<Configure>', self.on_preview_pane_resize)
        self.main_preview_paned_window.bind('<ButtonRelease-1>', self.on_preview_pane_resize)
        self.preview_paned_window.bind('<Configure>', self.on_preview_pane_resize)
        self.preview_paned_window.bind('<ButtonRelease-1>', self.on_preview_pane_resize)
        
        # Bind double-click to set equal pane sizes
        self.preview_paned_window.bind('<Double-Button-1>', self.set_equal_preview_pane_sizes)
    
    def create_image_preview_frame(self):
        """Create the image preview frame"""
        image_preview_frame = ttk.LabelFrame(self.preview_paned_window, text="Image Preview")
        self.preview_paned_window.add(image_preview_frame, weight=1)
        
        # Create frame to hold the image preview
        self.image_preview_frame = ttk.Frame(image_preview_frame)
        self.image_preview_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)
        
        # Create label to display the image
        self.image_preview_label = ttk.Label(
            self.image_preview_frame, 
            text="No image available - Start generation to see preview"
        )
        self.image_preview_label.pack(fill=tk.BOTH, expand=True)
        
        # Create status label
        self.image_preview_status_label = ttk.Label(
            image_preview_frame, 
            text="No image available"
        )
        self.image_preview_status_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.image_preview_status_label.bind("<Double-Button-1>", self.refresh_image_preview)
    
    def create_latent_preview_frame(self):
        """Create the latent preview frame"""
        latent_preview_frame = ttk.LabelFrame(self.preview_paned_window, text="Latent Preview")
        self.preview_paned_window.add(latent_preview_frame, weight=1)
        
        # Create frame to hold the video player
        self.video_frame = ttk.Frame(latent_preview_frame)
        self.video_frame.pack(fill=tk.BOTH, padx=1, pady=1, expand=True)
        
        # Initialize the latent preview player
        if MOVIEPY_AVAILABLE:
            try:
                self.latent_preview_player = MoviePyPlayer(master=self.video_frame, width=300, height=300)
                self.latent_preview_player.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
                self.latent_preview_player.set_scaling_mode('fit')
                print("Using MoviePyPlayer for latent previews")
            except Exception as e:
                print(f"Error initializing MoviePy player for latent preview: {e}")
                self.latent_preview_player = None
                ttk.Label(self.video_frame, text="Video player initialization failed.").pack(pady=10)
        else:
            self.latent_preview_player = None
            ttk.Label(self.video_frame, text="Video preview not available.").pack(pady=10)
        
        # Create status label
        self.latent_preview_label = ttk.Label(
            latent_preview_frame, 
            text="No preview available - Start generation to see preview"
        )
        self.latent_preview_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.latent_preview_label.bind("<Double-Button-1>", self.refresh_latent_preview)
    
    def create_output_preview_frame(self):
        """Create the output video preview frame"""
        output_preview_frame = ttk.LabelFrame(self.main_preview_paned_window, text="Output Video Preview")
        self.main_preview_paned_window.add(output_preview_frame, weight=1)
        
        # Create frame to hold the output video player
        self.output_video_frame = ttk.Frame(output_preview_frame)
        self.output_video_frame.pack(fill=tk.BOTH, padx=1, pady=1, expand=True)
        
        # Initialize the output video player
        if MOVIEPY_AVAILABLE:
            try:
                self.output_preview_player = MoviePyPlayer(master=self.output_video_frame, width=400, height=400)
                self.output_preview_player.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
                print("Using MoviePyPlayer for output previews")
            except Exception as e:
                print(f"Error initializing MoviePy player for output preview: {e}")
                self.output_preview_player = None
                ttk.Label(self.output_video_frame, text="Video player initialization failed.").pack(pady=10)
        else:
            self.output_preview_player = None
            ttk.Label(self.output_video_frame, text="Video preview not available.").pack(pady=10)
        
        # Create status label
        self.output_preview_label = ttk.Label(
            output_preview_frame, 
            text="No output video available - Complete generation to see result"
        )
        self.output_preview_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.output_preview_label.bind("<Double-Button-1>", self.refresh_output_preview)

    # Control button methods
    def force_stop_all(self):
        """Force terminate all FramePack processes"""
        try:
            # Create stop flag files
            stop_files = ["stop_framepack.flag", "stop_queue.flag", "skip_generation.flag"]
            for stop_file in stop_files:
                try:
                    with open(stop_file, 'w') as f:
                        f.write(f"Force stop all requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"Created {stop_file}")
                except Exception as e:
                    print(f"Error creating {stop_file}: {e}")

            print("Force Stop All: Created all stop flag files")

        except Exception as e:
            print(f"Error in force_stop_all: {e}")

    def stop_generation(self):
        """Stop the current generation"""
        try:
            # Create stop queue flag
            with open("stop_queue.flag", 'w') as f:
                f.write(f"Stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("Stop: Created stop_queue.flag")

        except Exception as e:
            print(f"Error in stop_generation: {e}")

    def skip_generation(self):
        """Skip the current generation"""
        try:
            # Create skip generation flag
            with open("skip_generation.flag", 'w') as f:
                f.write(f"Skip requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("Skip: Created skip_generation.flag")

        except Exception as e:
            print(f"Error in skip_generation: {e}")

    # Preview update methods
    def update_previews(self):
        """Update all preview displays"""
        try:
            # Update image preview
            self.update_image_preview()

            # Update latent preview
            self.update_latent_preview()

            # Update output preview
            self.update_output_preview()

        except Exception as e:
            print(f"Error updating previews: {e}")
        finally:
            # Schedule next update
            self.root.after(2000, self.update_previews)

    def update_image_preview(self):
        """Update the image preview"""
        try:
            if self.preview_state['image_loading']:
                return

            # Find latest start image
            image_path = self.find_latest_start_image()
            if image_path and image_path != self.preview_state['current_image']:
                if os.path.exists(image_path):
                    self.load_image_preview(image_path)

        except Exception as e:
            print(f"Error updating image preview: {e}")

    def update_latent_preview(self):
        """Update the latent preview"""
        try:
            if self.preview_state['latent_loading']:
                return

            # Find latest latent preview
            video_path = self.find_latest_latent_preview()
            if video_path and video_path != self.preview_state['current_latent']:
                if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
                    self.load_latent_preview(video_path)

        except Exception as e:
            print(f"Error updating latent preview: {e}")

    def update_output_preview(self):
        """Update the output preview"""
        try:
            if self.preview_state['output_loading']:
                return

            # Find latest output video
            video_path = self.find_latest_output_video()
            if video_path and video_path != self.preview_state['current_output']:
                if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
                    self.load_output_preview(video_path)

        except Exception as e:
            print(f"Error updating output preview: {e}")

    # File finding methods
    def find_latest_start_image(self):
        """Find the latest job-specific start image for the current generation"""
        try:
            import re
            import glob

            # Priority 1: Look for job-specific start images in outputs folder
            # Pattern: YYMMDD_HHMMSS_YYMMDD_HHMMSS_XXX_YYYY_start.png
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                start_pattern = os.path.join(outputs_dir, "*_start.png")
                start_files = glob.glob(start_pattern)

                # Filter for job-specific pattern and get most recent
                job_specific_files = []
                for file_path in start_files:
                    filename = os.path.basename(file_path)
                    if re.match(r'^\d+_\d+_\d+_\d+_\d+_\d+_start\.png$', filename):
                        job_specific_files.append((file_path, os.path.getmtime(file_path)))

                if job_specific_files:
                    # Get the most recent job-specific file
                    latest_file = max(job_specific_files, key=lambda x: x[1])
                    print(f"Found current image: {os.path.basename(latest_file[0])} (job-specific)")
                    return latest_file[0]

            # Priority 2: Look for job-specific start images in current directory
            start_files = glob.glob("*_start.png")
            job_specific_files = []
            for file_path in start_files:
                filename = os.path.basename(file_path)
                if re.match(r'^\d+_\d+_\d+_\d+_\d+_\d+_start\.png$', filename):
                    job_specific_files.append((file_path, os.path.getmtime(file_path)))

            if job_specific_files:
                latest_file = max(job_specific_files, key=lambda x: x[1])
                print(f"Found current image: {os.path.basename(latest_file[0])} (current dir)")
                return latest_file[0]

            # Priority 3: Check temp folder for current processing images (fallback)
            temp_dir = "temp"
            if os.path.exists(temp_dir):
                temp_files = []
                for file in os.listdir(temp_dir):
                    if file.lower().endswith('.png'):
                        if any(keyword in file.lower() for keyword in ['start', 'input', 'current', 'processing']):
                            file_path = os.path.join(temp_dir, file)
                            temp_files.append((file_path, os.path.getmtime(file_path)))

                if temp_files:
                    latest_file = max(temp_files, key=lambda x: x[1])
                    print(f"Found current image: {os.path.basename(latest_file[0])} (temp)")
                    return latest_file[0]

            # Priority 4: Generic start/input images (last resort)
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.webp']
            generic_files = []
            for file in os.listdir('.'):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    if any(keyword in file.lower() for keyword in ['start', 'input', 'source', 'original']):
                        # Skip if it's a job-specific file (already checked above)
                        if not re.match(r'^\d+_\d+_\d+_\d+_\d+_\d+_start\.png$', file):
                            generic_files.append((file, os.path.getmtime(file)))

            if generic_files:
                latest_file = max(generic_files, key=lambda x: x[1])
                print(f"Found current image: {os.path.basename(latest_file[0])} (generic)")
                return latest_file[0]

        except Exception as e:
            print(f"Error finding latest start image: {e}")
        return None

    def find_latest_latent_preview(self):
        """Find the latest latent preview file"""
        try:
            # Check temp folder first
            temp_dir = "temp"
            if os.path.exists(temp_dir):
                preview_files = [f for f in os.listdir(temp_dir)
                               if f.startswith("latest_latent_preview_") and f.endswith(".mp4")]
                if preview_files:
                    preview_files.sort(key=lambda x: os.path.getmtime(os.path.join(temp_dir, x)), reverse=True)
                    return os.path.join(temp_dir, preview_files[0])

            # Check latent_previews folder
            latent_dir = "latent_previews"
            if os.path.exists(latent_dir):
                preview_files = [f for f in os.listdir(latent_dir) if f.endswith(".mp4")]
                if preview_files:
                    preview_files.sort(key=lambda x: os.path.getmtime(os.path.join(latent_dir, x)), reverse=True)
                    return os.path.join(latent_dir, preview_files[0])

        except Exception as e:
            print(f"Error finding latest latent preview: {e}")
        return None

    def find_latest_output_video(self):
        """Find the latest output video"""
        try:
            # Check outputs folder
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                video_files = [f for f in os.listdir(outputs_dir) if f.endswith(".mp4")]
                if video_files:
                    video_files.sort(key=lambda x: os.path.getmtime(os.path.join(outputs_dir, x)), reverse=True)
                    return os.path.join(outputs_dir, video_files[0])

            # Check current directory
            video_files = [f for f in os.listdir('.') if f.endswith(".mp4")]
            if video_files:
                video_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                return video_files[0]

        except Exception as e:
            print(f"Error finding latest output video: {e}")
        return None

    # Preview loading methods
    def load_image_preview(self, image_path):
        """Load and display an image preview"""
        try:
            self.preview_state['image_loading'] = True

            # Load and resize the image
            img = Image.open(image_path)

            # Get available space in the preview frame
            self.image_preview_frame.update_idletasks()
            available_width = self.image_preview_frame.winfo_width()
            available_height = self.image_preview_frame.winfo_height()

            # Use reasonable defaults if frame hasn't been sized yet
            if available_width <= 1:
                available_width = 400
            if available_height <= 1:
                available_height = 300

            # Add padding
            max_width = max(200, available_width - 20)
            max_height = max(150, available_height - 20)

            # Calculate size maintaining aspect ratio
            img_width, img_height = img.size
            scale = min(max_width / img_width, max_height / img_height)
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)

            # Resize image
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(img)

            # Update the label
            self.image_preview_label.config(image=photo, text="")
            self.image_preview_photo = photo  # Keep reference

            # Update status
            self.image_preview_status_label.config(text=f"Loaded: {os.path.basename(image_path)}")
            self.preview_state['current_image'] = image_path

            print(f"Loaded image preview: {os.path.basename(image_path)}")

        except Exception as e:
            print(f"Error loading image preview: {e}")
            self.image_preview_status_label.config(text=f"Error loading image: {str(e)}")
        finally:
            self.preview_state['image_loading'] = False

    def load_latent_preview(self, video_path):
        """Load and display a latent preview video"""
        try:
            if not self.latent_preview_player:
                return

            self.preview_state['latent_loading'] = True

            # Load the video
            success = self.latent_preview_player.load(video_path)
            if success:
                self.preview_state['current_latent'] = video_path
                self.latent_preview_label.config(text=f"Playing: {os.path.basename(video_path)}")
                print(f"Loaded latent preview: {os.path.basename(video_path)}")
            else:
                self.latent_preview_label.config(text="Failed to load latent preview")

        except Exception as e:
            print(f"Error loading latent preview: {e}")
            self.latent_preview_label.config(text=f"Error: {str(e)}")
        finally:
            self.preview_state['latent_loading'] = False

    def load_output_preview(self, video_path):
        """Load and display an output preview video"""
        try:
            if not self.output_preview_player:
                return

            self.preview_state['output_loading'] = True

            # Load the video
            success = self.output_preview_player.load(video_path)
            if success:
                self.preview_state['current_output'] = video_path
                self.output_preview_label.config(text=f"Playing: {os.path.basename(video_path)}")
                print(f"Loaded output preview: {os.path.basename(video_path)}")
            else:
                self.output_preview_label.config(text="Failed to load output preview")

        except Exception as e:
            print(f"Error loading output preview: {e}")
            self.output_preview_label.config(text=f"Error: {str(e)}")
        finally:
            self.preview_state['output_loading'] = False

    # Refresh methods
    def refresh_image_preview(self, event=None):
        """Refresh the image preview"""
        try:
            print("Refreshing image preview...")
            self.preview_state['current_image'] = None
            self.image_preview_status_label.config(text="Refreshing image preview...")
            self.root.after(100, self.update_image_preview)
        except Exception as e:
            print(f"Error refreshing image preview: {e}")

    def refresh_latent_preview(self, event=None):
        """Refresh the latent preview"""
        try:
            print("Refreshing latent preview...")
            self.preview_state['current_latent'] = None
            if self.latent_preview_player:
                self.latent_preview_player.stop()
            self.latent_preview_label.config(text="Refreshing latent preview...")
            self.root.after(100, self.update_latent_preview)
        except Exception as e:
            print(f"Error refreshing latent preview: {e}")

    def refresh_output_preview(self, event=None):
        """Refresh the output preview"""
        try:
            print("Refreshing output preview...")
            self.preview_state['current_output'] = None
            if self.output_preview_player:
                self.output_preview_player.stop()
            self.output_preview_label.config(text="Refreshing output preview...")
            self.root.after(100, self.update_output_preview)
        except Exception as e:
            print(f"Error refreshing output preview: {e}")

    # Resize handling methods
    def on_preview_pane_resize(self, event=None):
        """Handle resizing of the preview panes"""
        try:
            # Schedule the resize operation to avoid conflicts during dragging
            if hasattr(self, '_resize_after_id'):
                self.root.after_cancel(self._resize_after_id)
            self._resize_after_id = self.root.after(50, self._perform_preview_resize)
        except Exception as e:
            print(f"Error scheduling preview resize: {e}")

    def _perform_preview_resize(self):
        """Perform the actual preview resizing"""
        try:
            # Update frames to get current dimensions
            self.root.update_idletasks()

            # Auto-center the sash to keep equal preview sizes
            self._auto_center_preview_sash()

            # Trigger image preview update if we have an image
            if hasattr(self, 'image_preview_photo') and self.image_preview_photo:
                self.root.after(50, self._resize_current_image_preview)

            # Trigger video player resizes
            if hasattr(self, 'latent_preview_player') and self.latent_preview_player:
                self.root.after(50, self._resize_current_latent_preview)

            if hasattr(self, 'output_preview_player') and self.output_preview_player:
                self.root.after(50, self._resize_current_output_preview)

        except Exception as e:
            print(f"Error performing preview resize: {e}")

    def _auto_center_preview_sash(self):
        """Automatically center the sash between image and latent previews"""
        try:
            if hasattr(self, 'preview_paned_window') and self.preview_paned_window:
                total_width = self.preview_paned_window.winfo_width()
                if total_width > 0:
                    half_width = total_width // 2
                    try:
                        self.preview_paned_window.sashpos(0, half_width)
                    except:
                        pass
        except:
            pass

    def set_equal_preview_pane_sizes(self, event=None):
        """Set equal sizes for image and latent preview panes when double-clicking the sash"""
        try:
            if hasattr(self, 'preview_paned_window') and self.preview_paned_window:
                self.root.update_idletasks()
                total_width = self.preview_paned_window.winfo_width()
                if total_width > 0:
                    half_width = total_width // 2
                    try:
                        self.preview_paned_window.sashpos(0, half_width)
                        print(f"Set equal preview pane sizes: {half_width}px each")
                    except Exception as e:
                        print(f"Error setting sash position: {e}")
        except Exception as e:
            print(f"Error setting equal preview pane sizes: {e}")

    def _resize_current_image_preview(self):
        """Resize the current image preview to fit the new frame size"""
        try:
            current_image = self.preview_state.get('current_image')
            if current_image and os.path.exists(current_image):
                self.load_image_preview(current_image)
        except Exception as e:
            print(f"Error resizing image preview: {e}")

    def _resize_current_latent_preview(self):
        """Resize the current latent preview to fit the new frame size"""
        try:
            if hasattr(self, 'latent_preview_player') and self.latent_preview_player:
                self.video_frame.update_idletasks()
                frame_width = self.video_frame.winfo_width()
                frame_height = self.video_frame.winfo_height()

                if frame_width > 10 and frame_height > 10:
                    available_width = max(200, frame_width - 6)
                    available_height = max(150, frame_height - 6)

                    if hasattr(self.latent_preview_player, 'resize_to_fit'):
                        try:
                            self.latent_preview_player.resize_to_fit(available_width, available_height)
                        except Exception as resize_error:
                            print(f"Could not resize latent video player: {resize_error}")
        except Exception as e:
            print(f"Error resizing latent preview: {e}")

    def _resize_current_output_preview(self):
        """Resize the current output preview to fit the new frame size"""
        try:
            if hasattr(self, 'output_preview_player') and self.output_preview_player:
                self.output_video_frame.update_idletasks()
                frame_width = self.output_video_frame.winfo_width()
                frame_height = self.output_video_frame.winfo_height()

                if frame_width > 10 and frame_height > 10:
                    available_width = max(200, frame_width - 6)
                    available_height = max(150, frame_height - 6)

                    if hasattr(self.output_preview_player, 'resize_to_fit'):
                        try:
                            self.output_preview_player.resize_to_fit(available_width, available_height)
                        except Exception as resize_error:
                            print(f"Could not resize output video player: {resize_error}")
        except Exception as e:
            print(f"Error resizing output preview: {e}")

    def on_window_resize(self, event=None):
        """Handle window resize events"""
        if event and event.widget == self.root:
            # Save window state when resized
            self.root.after(500, self.save_window_state)

    # Window state management
    def save_window_state(self):
        """Save window position, size, and pane positions"""
        try:
            config = configparser.ConfigParser()
            config_file = "standalone_preview_window.ini"

            # Load existing config if it exists
            if os.path.exists(config_file):
                config.read(config_file)

            # Ensure section exists
            if not config.has_section('window'):
                config.add_section('window')
            if not config.has_section('panes'):
                config.add_section('panes')

            # Save window geometry
            geometry = self.root.geometry()
            config.set('window', 'geometry', geometry)

            # Save window state (normal/maximized)
            state = self.root.state()
            config.set('window', 'state', state)

            # Save pane positions
            try:
                # Save main preview paned window sash positions
                if hasattr(self, 'main_preview_paned_window'):
                    sash_positions = []
                    for i in range(len(self.main_preview_paned_window.panes()) - 1):
                        try:
                            pos = self.main_preview_paned_window.sashpos(i)
                            sash_positions.append(str(pos))
                        except:
                            pass
                    if sash_positions:
                        config.set('panes', 'main_sash_positions', ','.join(sash_positions))

                # Save preview paned window sash positions
                if hasattr(self, 'preview_paned_window'):
                    sash_positions = []
                    for i in range(len(self.preview_paned_window.panes()) - 1):
                        try:
                            pos = self.preview_paned_window.sashpos(i)
                            sash_positions.append(str(pos))
                        except:
                            pass
                    if sash_positions:
                        config.set('panes', 'preview_sash_positions', ','.join(sash_positions))
            except Exception as e:
                print(f"Error saving pane positions: {e}")

            # Write config file
            with open(config_file, 'w') as f:
                config.write(f)

        except Exception as e:
            print(f"Error saving window state: {e}")

    def load_window_state(self):
        """Load window position, size, and pane positions"""
        try:
            config_file = "standalone_preview_window.ini"
            if not os.path.exists(config_file):
                return

            config = configparser.ConfigParser()
            config.read(config_file)

            # Restore window geometry
            if config.has_option('window', 'geometry'):
                geometry = config.get('window', 'geometry')
                try:
                    self.root.geometry(geometry)
                    print(f"Restored window geometry: {geometry}")
                except Exception as e:
                    print(f"Error restoring window geometry: {e}")

            # Restore window state
            if config.has_option('window', 'state'):
                state = config.get('window', 'state')
                try:
                    if state == 'zoomed':  # maximized
                        self.root.state('zoomed')
                        print("Restored maximized window state")
                except Exception as e:
                    print(f"Error restoring window state: {e}")

            # Restore pane positions (after a delay to ensure UI is ready)
            self.root.after(1000, self._restore_pane_positions, config)

        except Exception as e:
            print(f"Error loading window state: {e}")

    def _restore_pane_positions(self, config):
        """Restore pane positions from config"""
        try:
            # Restore main preview paned window sash positions
            if config.has_option('panes', 'main_sash_positions'):
                main_sash_str = config.get('panes', 'main_sash_positions')
                if main_sash_str and hasattr(self, 'main_preview_paned_window'):
                    try:
                        sash_positions = [int(pos) for pos in main_sash_str.split(',')]
                        for i, pos in enumerate(sash_positions):
                            if i < len(self.main_preview_paned_window.panes()) - 1:
                                try:
                                    self.main_preview_paned_window.sashpos(i, pos)
                                except Exception as e:
                                    print(f"Error setting main sash position {i} to {pos}: {e}")
                        print(f"Restored main pane sizes: {sash_positions}")
                    except Exception as e:
                        print(f"Error restoring main pane sizes: {e}")

            # Restore preview paned window sash positions
            if config.has_option('panes', 'preview_sash_positions'):
                preview_sash_str = config.get('panes', 'preview_sash_positions')
                if preview_sash_str and hasattr(self, 'preview_paned_window'):
                    try:
                        sash_positions = [int(pos) for pos in preview_sash_str.split(',')]
                        for i, pos in enumerate(sash_positions):
                            if i < len(self.preview_paned_window.panes()) - 1:
                                try:
                                    self.preview_paned_window.sashpos(i, pos)
                                except Exception as e:
                                    print(f"Error setting preview sash position {i} to {pos}: {e}")
                        print(f"Restored preview pane sizes: {sash_positions}")
                    except Exception as e:
                        print(f"Error restoring preview pane sizes: {e}")

        except Exception as e:
            print(f"Error restoring pane positions: {e}")

    def on_closing(self):
        """Handle window closing"""
        try:
            # Save window state
            self.save_window_state()

            # Stop video players
            if self.latent_preview_player:
                try:
                    self.latent_preview_player.stop()
                except:
                    pass

            if self.output_preview_player:
                try:
                    self.output_preview_player.stop()
                except:
                    pass

            print("Standalone preview window closing...")

        except Exception as e:
            print(f"Error during window closing: {e}")
        finally:
            self.root.destroy()


def main():
    """Main function to run the standalone preview window"""
    root = tk.Tk()
    app = StandalonePreviewWindow(root)
    root.mainloop()


if __name__ == "__main__":
    main()
