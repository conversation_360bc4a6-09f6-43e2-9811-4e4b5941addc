#!/usr/bin/env python
"""
Image Crop Test App (.pyw version - no console)

A GUI application for testing the image cropping functionality from FramePack.
Allows users to load images via dialog or drag-and-drop, adjust cropping settings,
and preview the results in real-time.

Features:
- Load images via file dialog or drag-and-drop
- All FramePack cropping controls (fill percentage, padding, skip options)
- Real-time preview of original and cropped images
- Apply button to test cropping with current settings
- Status indicators for multi-face and no-face conditions
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
from PIL import Image, ImageTk
import threading
import cv2

# Try to import TkinterDnD for drag and drop support
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    TKDND_AVAILABLE = True
except ImportError:
    TKDND_AVAILABLE = False
    print("TkinterDnD not available - drag and drop disabled")

class ImageCropTestApp:
    def __init__(self):
        # Initialize the main window
        if TKDND_AVAILABLE:
            self.root = TkinterDnD.Tk()
        else:
            self.root = tk.Tk()
        
        self.root.title("Image Crop Test App")
        self.root.geometry("1200x800")
        
        # Initialize variables
        self.current_image_path = None
        self.original_image = None
        self.cropped_image = None
        
        # Face cropping settings (matching FramePack GUI)
        self.face_fill_percentage = tk.IntVar(value=60)  # Face fill percentage (10-95, default: 60)
        self.face_padding_enabled = tk.BooleanVar(value=False)  # Enable padding
        self.face_padding_pixels = tk.IntVar(value=0)  # Padding pixels (0-500, default: 0)
        self.face_padding_percent = tk.IntVar(value=10)  # Padding percent (1-100, default: 10)
        self.face_padding_mode = tk.StringVar(value="Pixels")  # Padding mode: "Pixels" or "Percent"
        self.face_padding_side = tk.StringVar(value="Top and Bottom")  # Padding side
        self.skip_multi_face = tk.BooleanVar(value=True)  # Skip multi-face images
        self.skip_no_face = tk.BooleanVar(value=False)  # Skip no-face images
        
        # UI state
        self.processing = False
        
        # Create the UI
        self.create_ui()
        
        # Set up drag and drop if available
        if TKDND_AVAILABLE:
            self.setup_drag_drop()
    
    def create_ui(self):
        """Create the user interface"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Top section: File loading
        file_frame = ttk.LabelFrame(main_frame, text="Load Image", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection button
        self.load_button = ttk.Button(file_frame, text="Load Image", command=self.load_image)
        self.load_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Current file label
        self.file_label = ttk.Label(file_frame, text="No image loaded")
        self.file_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Middle section: Settings and previews
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel: Settings
        settings_frame = ttk.LabelFrame(content_frame, text="Crop Settings", padding="10")
        settings_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        self.create_settings_panel(settings_frame)
        
        # Right panel: Image previews
        preview_frame = ttk.Frame(content_frame)
        preview_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_preview_panel(preview_frame)
        
        # Bottom section: Action buttons and status
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Apply button
        self.apply_button = ttk.Button(bottom_frame, text="Apply Crop", command=self.apply_crop)
        self.apply_button.pack(side=tk.LEFT, padx=(0, 10))
        self.apply_button.config(state='disabled')
        
        # Status label
        self.status_label = ttk.Label(bottom_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_settings_panel(self, parent):
        """Create the settings panel with all crop controls"""
        # Face fill percentage
        fill_frame = ttk.Frame(parent)
        fill_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(fill_frame, text="Face Fill %:").pack(anchor=tk.W)
        fill_scale = ttk.Scale(fill_frame, from_=10, to=95, variable=self.face_fill_percentage, orient=tk.HORIZONTAL)
        fill_scale.pack(fill=tk.X, pady=(5, 0))
        
        fill_value_label = ttk.Label(fill_frame, text="60%")
        fill_value_label.pack(anchor=tk.W)
        
        # Update label when scale changes
        def update_fill_label(*args):
            fill_value_label.config(text=f"{self.face_fill_percentage.get()}%")
        self.face_fill_percentage.trace('w', update_fill_label)
        
        # Padding controls - reorganized layout
        padding_frame = ttk.LabelFrame(parent, text="Padding", padding="5")
        padding_frame.pack(fill=tk.X, pady=(0, 10))
        
        # First row: Enable Padding checkbox and Side selector
        padding_row1 = ttk.Frame(padding_frame)
        padding_row1.pack(fill=tk.X, pady=(0, 2))
        
        # Enable padding checkbox
        padding_enable_cb = ttk.Checkbutton(padding_row1, text="Enable Padding", variable=self.face_padding_enabled)
        padding_enable_cb.pack(side=tk.LEFT, padx=(0, 20))
        
        # Side selector on the same row
        ttk.Label(padding_row1, text="Side:").pack(side=tk.LEFT)
        side_combo = ttk.Combobox(padding_row1, textvariable=self.face_padding_side, width=12, state="readonly")
        side_combo['values'] = ("Top", "Bottom", "Left", "Right", "Top and Bottom", "Left and Right")
        side_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # Second row (inset): Mode selector and Pixels/Percent controls
        padding_row2 = ttk.Frame(padding_frame)
        padding_row2.pack(fill=tk.X, padx=(20, 0))  # Inset with left padding
        
        # Padding mode selection
        ttk.Label(padding_row2, text="Mode:").pack(side=tk.LEFT)
        mode_combo = ttk.Combobox(padding_row2, textvariable=self.face_padding_mode, width=8, state="readonly")
        mode_combo['values'] = ("Pixels", "Percent")
        mode_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        # Pixels control
        self.pixels_label = ttk.Label(padding_row2, text="Pixels:")
        self.pixels_spinbox = ttk.Spinbox(padding_row2, from_=0, to=500, textvariable=self.face_padding_pixels, width=8)
        
        # Percent control  
        self.percent_label = ttk.Label(padding_row2, text="Percent:")
        self.percent_spinbox = ttk.Spinbox(padding_row2, from_=1, to=100, textvariable=self.face_padding_percent, width=8)
        
        # Update padding controls visibility
        def update_padding_controls(*args):
            mode = self.face_padding_mode.get()
            if mode == "Pixels":
                self.pixels_label.pack(side=tk.LEFT)
                self.pixels_spinbox.pack(side=tk.LEFT, padx=(5, 10))
                self.percent_label.pack_forget()
                self.percent_spinbox.pack_forget()
            else:
                self.percent_label.pack(side=tk.LEFT)
                self.percent_spinbox.pack(side=tk.LEFT, padx=(5, 10))
                self.pixels_label.pack_forget()
                self.pixels_spinbox.pack_forget()
        
        self.face_padding_mode.trace('w', update_padding_controls)
        mode_combo.bind('<<ComboboxSelected>>', update_padding_controls)
        update_padding_controls()  # Initial setup
        
        # Skip options
        skip_frame = ttk.LabelFrame(parent, text="Skip Options", padding="5")
        skip_frame.pack(fill=tk.X, pady=(0, 10))
        
        skip_multi_cb = ttk.Checkbutton(skip_frame, text="Skip Multi-Face Images", variable=self.skip_multi_face)
        skip_multi_cb.pack(anchor=tk.W, pady=(0, 2))
        
        skip_no_face_cb = ttk.Checkbutton(skip_frame, text="Skip No-Face Images", variable=self.skip_no_face)
        skip_no_face_cb.pack(anchor=tk.W)
    
    def create_preview_panel(self, parent):
        """Create the image preview panel"""
        # Original image preview
        original_frame = ttk.LabelFrame(parent, text="Original Image", padding="5")
        original_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        self.original_canvas = tk.Canvas(original_frame, bg='white', width=400, height=300)
        self.original_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Cropped image preview
        cropped_frame = ttk.LabelFrame(parent, text="Cropped Preview", padding="5")
        cropped_frame.pack(fill=tk.BOTH, expand=True)
        
        self.cropped_canvas = tk.Canvas(cropped_frame, bg='white', width=400, height=300)
        self.cropped_canvas.pack(fill=tk.BOTH, expand=True)
    
    def setup_drag_drop(self):
        """Set up drag and drop functionality"""
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_drop)
    
    def on_drop(self, event):
        """Handle dropped files"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]  # Take the first file
            if self.is_image_file(file_path):
                self.load_image_from_path(file_path)
            else:
                messagebox.showerror("Error", "Please drop an image file (PNG, JPG, JPEG, BMP, GIF)")
    
    def is_image_file(self, file_path):
        """Check if file is a supported image format"""
        valid_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp'}
        return os.path.splitext(file_path.lower())[1] in valid_extensions
    
    def load_image(self):
        """Load an image via file dialog"""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif *.tiff *.webp"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_image_from_path(file_path)
    
    def load_image_from_path(self, file_path):
        """Load an image from the given path"""
        try:
            # Load the image
            self.original_image = Image.open(file_path)
            self.current_image_path = file_path
            
            # Update UI
            self.file_label.config(text=os.path.basename(file_path))
            self.apply_button.config(state='normal')
            self.status_label.config(text=f"Loaded: {self.original_image.size[0]}x{self.original_image.size[1]}")
            
            # Display original image
            self.display_original_image()
            
            # Clear cropped preview
            self.cropped_canvas.delete("all")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            self.status_label.config(text="Error loading image")

    def display_original_image(self):
        """Display the original image in the preview canvas"""
        if not self.original_image:
            return

        # Get canvas size
        canvas_width = self.original_canvas.winfo_width()
        canvas_height = self.original_canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            # Canvas not ready yet, try again later
            self.root.after(100, self.display_original_image)
            return

        # Calculate scaling to fit canvas
        img_width, img_height = self.original_image.size
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # Don't upscale

        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        display_image = self.original_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Convert to PhotoImage and display
        self.original_photo = ImageTk.PhotoImage(display_image)

        # Clear canvas and center image
        self.original_canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        self.original_canvas.create_image(x, y, anchor=tk.NW, image=self.original_photo)

    def apply_crop(self):
        """Apply cropping with current settings"""
        if not self.original_image or self.processing:
            return

        self.processing = True
        self.apply_button.config(state='disabled')
        self.status_label.config(text="Processing...")

        # Run cropping in a separate thread to avoid blocking UI
        thread = threading.Thread(target=self._crop_image_thread)
        thread.daemon = True
        thread.start()

    def _crop_image_thread(self):
        """Crop image in a separate thread"""
        try:
            # Import the face cropping functionality
            from image_face_cropper import crop_image_to_face

            # Prepare settings
            settings = {
                'face_fill_percentage': self.face_fill_percentage.get(),
                'face_padding_enabled': self.face_padding_enabled.get(),
                'face_padding_pixels': self.face_padding_pixels.get(),
                'face_padding_percent': self.face_padding_percent.get(),
                'face_padding_mode': self.face_padding_mode.get(),
                'face_padding_side': self.face_padding_side.get(),
                'skip_multi_face': self.skip_multi_face.get(),
                'skip_no_face': self.skip_no_face.get()
            }

            # Apply cropping
            result = crop_image_to_face(self.current_image_path, settings)

            # Update UI in main thread
            self.root.after(0, self._crop_complete, result)

        except Exception as e:
            self.root.after(0, self._crop_error, str(e))

    def _crop_complete(self, result):
        """Handle crop completion"""
        self.processing = False
        self.apply_button.config(state='normal')

        if result['success']:
            # Load cropped image
            try:
                self.cropped_image = Image.open(result['output_path'])
                self.display_cropped_image()
                self.status_label.config(text=f"Cropped: {self.cropped_image.size[0]}x{self.cropped_image.size[1]}")
            except Exception as e:
                self.status_label.config(text=f"Error loading cropped image: {str(e)}")
        else:
            # Handle skip conditions
            if result.get('skipped'):
                reason = result.get('skip_reason', 'Unknown reason')
                self.status_label.config(text=f"Skipped: {reason}")
                self.cropped_canvas.delete("all")
                self.cropped_canvas.create_text(
                    200, 150, text=f"SKIPPED\n{reason}",
                    font=("Arial", 14), fill="red", justify=tk.CENTER
                )
            else:
                error_msg = result.get('error', 'Unknown error')
                self.status_label.config(text=f"Error: {error_msg}")
                messagebox.showerror("Crop Error", error_msg)

    def _crop_error(self, error_msg):
        """Handle crop error"""
        self.processing = False
        self.apply_button.config(state='normal')
        self.status_label.config(text=f"Error: {error_msg}")
        messagebox.showerror("Crop Error", error_msg)

    def display_cropped_image(self):
        """Display the cropped image in the preview canvas"""
        if not self.cropped_image:
            return

        # Get canvas size
        canvas_width = self.cropped_canvas.winfo_width()
        canvas_height = self.cropped_canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            # Canvas not ready yet, try again later
            self.root.after(100, self.display_cropped_image)
            return

        # Calculate scaling to fit canvas
        img_width, img_height = self.cropped_image.size
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # Don't upscale

        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        display_image = self.cropped_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Convert to PhotoImage and display
        self.cropped_photo = ImageTk.PhotoImage(display_image)

        # Clear canvas and center image
        self.cropped_canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        self.cropped_canvas.create_image(x, y, anchor=tk.NW, image=self.cropped_photo)


def main():
    app = ImageCropTestApp()
    app.root.mainloop()

if __name__ == "__main__":
    main()
